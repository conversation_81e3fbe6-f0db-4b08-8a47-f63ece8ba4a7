"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { toast } from "sonner";
import { submitFoodChainRegistrationRequest } from "@/server/user";
import { Loader2, Building2, Phone, Mail, MapPin, Globe } from "lucide-react";

interface FormData {
  contactPersonName: string;
  email: string;
  phone: string;
  businessName: string;
  businessType: string;
  subcategory: string;
  city: string;
  state: string;
  address: string;
  message: string;
  estimatedOutlets: number;
  website: string;
}

const ContactPage = () => {
  const [loading, setLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const [formData, setFormData] = useState<FormData>({
    contactPersonName: "",
    email: "",
    phone: "",
    businessName: "",
    businessType: "",
    subcategory: "restaurant",
    city: "",
    state: "",
    address: "",
    message: "",
    estimatedOutlets: 1,
    website: "",
  });

  const businessTypes = [
    { value: "individual", label: "Individual" },
    { value: "proprietorship", label: "Proprietorship" },
    { value: "partnership", label: "Partnership" },
    { value: "llp", label: "Limited Liability Partnership (LLP)" },
    { value: "private_limited", label: "Private Limited Company" },
    { value: "public_limited", label: "Public Limited Company" },
    { value: "trust", label: "Trust" },
    { value: "society", label: "Society" },
    { value: "ngo", label: "NGO" },
    { value: "educational_institutes", label: "Educational Institute" },
    { value: "not_yet_registered", label: "Not Yet Registered" },
    { value: "other", label: "Other" },
  ];

  const subcategories = [
    { value: "restaurant", label: "Restaurant" },
    { value: "online_food_ordering", label: "Online Food Ordering" },
    { value: "food_court", label: "Food Court" },
    { value: "catering", label: "Catering" },
    { value: "alcohol", label: "Alcohol" },
    {
      value: "restaurant_search_and_booking",
      label: "Restaurant Search & Booking",
    },
    { value: "dairy_products", label: "Dairy Products" },
    { value: "bakeries", label: "Bakeries" },
  ];

  const handleInputChange = (field: keyof FormData, value: string | number) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (
      !formData.contactPersonName ||
      !formData.email ||
      !formData.phone ||
      !formData.businessName ||
      !formData.businessType ||
      !formData.city ||
      !formData.state
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      toast.error("Please enter a valid email address");
      return;
    }

    // Validate phone format (basic validation)
    const phoneRegex = /^[+]?[\d\s\-\(\)]{10,}$/;
    if (!phoneRegex.test(formData.phone)) {
      toast.error("Please enter a valid phone number");
      return;
    }

    setLoading(true);
    try {
      const response = await submitFoodChainRegistrationRequest(formData);

      if (response.success) {
        setSubmitted(true);
        toast.success(
          "Registration request submitted successfully! Our team will contact you soon."
        );
      } else {
        toast.error(
          response.message || "Failed to submit registration request"
        );
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("An error occurred while submitting your request");
    } finally {
      setLoading(false);
    }
  };

  if (submitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-4">
              <Building2 className="w-8 h-8 text-green-600" />
            </div>
            <CardTitle className="text-2xl text-green-600">
              Request Submitted!
            </CardTitle>
            <CardDescription>
              Thank you for your interest in Butler. Our team will review your
              application and contact you within 2-3 business days.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center">
            <Button
              onClick={() => {
                setSubmitted(false);
                setFormData({
                  contactPersonName: "",
                  email: "",
                  phone: "",
                  businessName: "",
                  businessType: "",
                  subcategory: "restaurant",
                  city: "",
                  state: "",
                  address: "",
                  message: "",
                  estimatedOutlets: 1,
                  website: "",
                });
              }}
              variant="outline"
              className="w-full"
            >
              Submit Another Request
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl font-bold text-gray-900 mb-4">
            Partner with Butler
          </h1>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Join the Butler platform and transform your restaurant business with
            our AI-powered ordering system
          </p>
        </div>

        {/* Registration Form */}
        <Card className="shadow-xl">
          <CardHeader>
            <CardTitle className="text-2xl flex items-center gap-2">
              <Building2 className="w-6 h-6 text-blue-600" />
              Food Chain Registration
            </CardTitle>
            <CardDescription>
              Fill out the form below to register your food chain with Butler.
              Our team will review your application and get back to you soon.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Contact Person Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-2">
                  <Label htmlFor="contactPersonName">
                    Contact Person Name *
                  </Label>
                  <Input
                    id="contactPersonName"
                    value={formData.contactPersonName}
                    onChange={(e) =>
                      handleInputChange("contactPersonName", e.target.value)
                    }
                    placeholder="Enter your full name"
                    required
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address *</Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="email"
                      type="email"
                      value={formData.email}
                      onChange={(e) =>
                        handleInputChange("email", e.target.value)
                      }
                      placeholder="<EMAIL>"
                      className="pl-10"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Phone Number *</Label>
                <div className="relative">
                  <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="phone"
                    value={formData.phone}
                    onChange={(e) => handleInputChange("phone", e.target.value)}
                    placeholder="+91 9876543210"
                    className="pl-10"
                    required
                  />
                </div>
              </div>

              {/* Business Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">
                  Business Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="businessName">Business Name *</Label>
                    <Input
                      id="businessName"
                      value={formData.businessName}
                      onChange={(e) =>
                        handleInputChange("businessName", e.target.value)
                      }
                      placeholder="Your restaurant/food chain name"
                      required
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="businessType">Business Type *</Label>
                    <Select
                      value={formData.businessType}
                      onValueChange={(value) =>
                        handleInputChange("businessType", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select business type" />
                      </SelectTrigger>
                      <SelectContent>
                        {businessTypes.map((type) => (
                          <SelectItem key={type.value} value={type.value}>
                            {type.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                  <div className="space-y-2">
                    <Label htmlFor="subcategory">Business Category</Label>
                    <Select
                      value={formData.subcategory}
                      onValueChange={(value) =>
                        handleInputChange("subcategory", value)
                      }
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select category" />
                      </SelectTrigger>
                      <SelectContent>
                        {subcategories.map((category) => (
                          <SelectItem
                            key={category.value}
                            value={category.value}
                          >
                            {category.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="estimatedOutlets">
                      Estimated Number of Outlets
                    </Label>
                    <Input
                      id="estimatedOutlets"
                      type="number"
                      min="1"
                      value={formData.estimatedOutlets}
                      onChange={(e) =>
                        handleInputChange(
                          "estimatedOutlets",
                          parseInt(e.target.value) || 1
                        )
                      }
                      placeholder="1"
                    />
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  <Label htmlFor="website">Website (Optional)</Label>
                  <div className="relative">
                    <Globe className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      id="website"
                      value={formData.website}
                      onChange={(e) =>
                        handleInputChange("website", e.target.value)
                      }
                      placeholder="https://yourwebsite.com"
                      className="pl-10"
                    />
                  </div>
                </div>
              </div>

              {/* Location Information */}
              <div className="border-t pt-6">
                <h3 className="text-lg font-semibold mb-4">
                  Location Information
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="city">City *</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="city"
                        value={formData.city}
                        onChange={(e) =>
                          handleInputChange("city", e.target.value)
                        }
                        placeholder="Enter city"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="state">State *</Label>
                    <Input
                      id="state"
                      value={formData.state}
                      onChange={(e) =>
                        handleInputChange("state", e.target.value)
                      }
                      placeholder="Enter state"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2 mt-4">
                  <Label htmlFor="address">Full Address (Optional)</Label>
                  <Textarea
                    id="address"
                    value={formData.address}
                    onChange={(e) =>
                      handleInputChange("address", e.target.value)
                    }
                    placeholder="Enter complete business address"
                    rows={3}
                  />
                </div>
              </div>

              {/* Additional Information */}
              <div className="border-t pt-6">
                <div className="space-y-2">
                  <Label htmlFor="message">
                    Additional Information (Optional)
                  </Label>
                  <Textarea
                    id="message"
                    value={formData.message}
                    onChange={(e) =>
                      handleInputChange("message", e.target.value)
                    }
                    placeholder="Tell us more about your business, special requirements, or any questions you have..."
                    rows={4}
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="pt-6">
                <Button
                  type="submit"
                  disabled={loading}
                  className="w-full bg-blue-600 hover:bg-blue-700 text-white py-3 text-lg"
                >
                  {loading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      Submitting Request...
                    </>
                  ) : (
                    "Submit Registration Request"
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <div className="mt-12 text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Need Help?</h2>
          <p className="text-gray-600 mb-6">
            Our team is here to assist you with any questions about joining
            Butler
          </p>
          <div className="flex flex-col sm:flex-row justify-center gap-4">
            <div className="flex items-center justify-center gap-2 text-gray-700">
              <Mail className="w-5 h-5" />
              <span><EMAIL></span>
            </div>
            <div className="flex items-center justify-center gap-2 text-gray-700">
              <Phone className="w-5 h-5" />
              <span>+91 9876543210</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactPage;
