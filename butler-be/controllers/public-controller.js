import User from "../models/User.js";
import FoodChain from "../models/FoodChain.js";
import FoodChainRegistrationRequest from "../models/FoodChainRegistrationRequest.js";
import bcrypt from "bcrypt";
import jwt from "jsonwebtoken";
import crypto from "crypto";
import { createAuditLog } from "../middlewares/auditLogger.js";

export const adminLogin = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    // Find user with any role
    const user = await User.findOne({ email });
    if (!user) {
      return res.status(404).json({ message: "User not found" });
    }

    // Check if user has an allowed role for admin dashboard
    const allowedRoles = ["admin", "manager", "cashier"];
    if (!allowedRoles.includes(user.role)) {
      return res.status(403).json({
        success: false,
        message:
          "Access denied. Your role does not have permission to access the admin dashboard.",
      });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: "Invalid password" });
    }

    // Update last login time
    user.lastLogin = new Date();

    // Check if this is the first login for a user created by admin
    const isFirstTimeLogin = user.isFirstLogin === true;

    // If it's not the first login, clear the flag
    if (user.isFirstLogin) {
      user.isFirstLogin = false;
    }

    await user.save();

    // Generate token with no expiration (long-lasting)
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        foodChain: user.foodChain,
        role: user.role,
      },
      process.env.JWT_SECRET
      // No expiresIn parameter means the token never expires
    );

    res.status(200).json({
      success: true,
      data: {
        token,
        user: {
          email: user.email,
          name: user.name,
          role: user.role,
          foodChain: user.foodChain,
          isFirstTimeLogin,
          createdBy: user.createdBy,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error logging in",
      error: error.message,
    });
  }
};

export const forgetPassword = async (req, res) => {
  try {
    const { email, role = "admin" } = req.body;

    if (!email) {
      return res.status(400).json({ message: "Email is required" });
    }

    // Find user based on role
    const user = await User.findOne({ email, role });
    if (!user) {
      return res.status(404).json({
        success: false,
        message: `User with role ${role} not found`,
      });
    }

    // Generate reset token (random string)
    const resetToken = crypto.randomBytes(32).toString("hex");

    // Hash the token for security
    const hashedToken = crypto
      .createHash("sha256")
      .update(resetToken)
      .digest("hex");

    // Set token and expiration (1 hour)
    user.resetPasswordToken = hashedToken;
    user.resetPasswordExpires = Date.now() + 3600000; // 1 hour
    await user.save();

    // Create reset URL
    const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}?role=${role}`;

    // Send reset email using nodemailer
    try {
      const { sendPasswordResetEmail } = await import(
        "../utils/emailService.js"
      );
      await sendPasswordResetEmail(user, resetUrl, role);
    } catch (emailError) {
      console.error("Error sending password reset email:", emailError);
      // Continue with the response even if email fails
    }

    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "password_reset",
      resourceType: "user",
      resourceId: user._id,
      description: `Password reset requested for ${email}`,
      status: "success",
      foodChainId: user.foodChain || null,
    });

    res.status(200).json({
      success: true,
      message: "Password reset email sent",
    });
  } catch (error) {
    console.error("Error in forgetPassword:", error);
    res.status(500).json({
      success: false,
      message: "Error sending password reset email",
      error: error.message,
    });
  }
};

export const resetPassword = async (req, res) => {
  try {
    const { token, password, role = "admin" } = req.body;

    if (!token || !password) {
      return res.status(400).json({
        success: false,
        message: "Token and password are required",
      });
    }

    // Hash the token from the URL to compare with the stored hashed token
    const hashedToken = crypto.createHash("sha256").update(token).digest("hex");

    // Find user with the token and check if token is still valid
    const user = await User.findOne({
      resetPasswordToken: hashedToken,
      resetPasswordExpires: { $gt: Date.now() },
      role,
    });

    if (!user) {
      return res.status(400).json({
        success: false,
        message: "Invalid or expired token",
      });
    }

    // Hash the new password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Update user password and clear reset token fields
    user.password = hashedPassword;
    user.resetPasswordToken = undefined;
    user.resetPasswordExpires = undefined;
    user.lastPasswordChange = Date.now();
    await user.save();

    // Send password reset confirmation email
    try {
      const { sendPasswordResetConfirmationEmail } = await import(
        "../utils/emailService.js"
      );
      await sendPasswordResetConfirmationEmail(user);
    } catch (emailError) {
      console.error(
        "Error sending password reset confirmation email:",
        emailError
      );
      // Continue with the response even if email fails
    }

    // Create audit log
    await createAuditLog({
      userId: user._id,
      action: "password_change",
      resourceType: "user",
      resourceId: user._id,
      description: `Password reset completed for ${user.email}`,
      status: "success",
      foodChainId: user.foodChain || null,
    });

    res.status(200).json({
      success: true,
      message: "Password has been reset successfully",
    });
  } catch (error) {
    console.error("Error in resetPassword:", error);
    res.status(500).json({
      success: false,
      message: "Error resetting password",
      error: error.message,
    });
  }
};

export const superAdminLogin = async (req, res) => {
  try {
    const { email, password } = req.body;

    if (!email || !password) {
      return res
        .status(400)
        .json({ message: "Email and password are required" });
    }

    // Find super admin user
    const user = await User.findOne({ email, role: "super_admin" });
    if (!user || user.role !== "super_admin") {
      return res.status(404).json({ message: "Super admin not found" });
    }

    // Check password
    const isValidPassword = await bcrypt.compare(password, user.password);
    if (!isValidPassword) {
      return res.status(401).json({ message: "Invalid password" });
    }

    // Generate token with no expiration (long-lasting)
    const token = jwt.sign(
      {
        userId: user._id,
        email: user.email,
        role: user.role,
      },
      process.env.JWT_SECRET_SUPER_ADMIN
      // No expiresIn parameter means the token never expires
    );

    res.status(200).json({
      success: true,
      data: {
        token,
        user: {
          email: user.email,
          name: user.name,
          role: user.role,
        },
      },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error logging in",
      error: error.message,
    });
  }
};

export const getTheme = async (req, res) => {
  try {
    const { foodChainId } = req.params;

    const foodChain = await FoodChain.findById(foodChainId).select(
      "theme name"
    );
    if (!foodChain) {
      return res.status(404).json({ message: "Food chain not found" });
    }
    res.status(200).json({
      success: true,
      data: { ...foodChain.theme, name: foodChain.name },
    });
  } catch (error) {
    res.status(500).json({
      success: false,
      message: "Error fetching theme",
      error: error.message,
    });
  }
};

export const submitFoodChainRegistrationRequest = async (req, res) => {
  try {
    const {
      contactPersonName,
      email,
      phone,
      businessName,
      businessType,
      subcategory,
      city,
      state,
      address,
      message,
      estimatedOutlets,
      website,
    } = req.body;

    // Validate required fields
    if (
      !contactPersonName ||
      !email ||
      !phone ||
      !businessName ||
      !businessType ||
      !city ||
      !state
    ) {
      return res.status(400).json({
        success: false,
        message:
          "Contact person name, email, phone, business name, business type, city, and state are required",
      });
    }

    // Check if a request with the same email already exists and is pending
    const existingRequest = await FoodChainRegistrationRequest.findOne({
      email,
      status: "pending",
    });

    if (existingRequest) {
      return res.status(400).json({
        success: false,
        message:
          "A registration request with this email is already pending review",
      });
    }

    // Create new registration request
    const registrationRequest = new FoodChainRegistrationRequest({
      contactPersonName,
      email,
      phone,
      businessName,
      businessType,
      subcategory: subcategory || "restaurant",
      city,
      state,
      address,
      message,
      estimatedOutlets: estimatedOutlets || 1,
      website,
    });

    await registrationRequest.save();

    res.status(201).json({
      success: true,
      message:
        "Registration request submitted successfully. Our team will contact you soon.",
      data: {
        id: registrationRequest._id,
        businessName: registrationRequest.businessName,
        status: registrationRequest.status,
      },
    });
  } catch (error) {
    console.error("Error submitting registration request:", error);
    res.status(500).json({
      success: false,
      message: "Error submitting registration request",
      error: error.message,
    });
  }
};
