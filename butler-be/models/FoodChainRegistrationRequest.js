import mongoose from "mongoose";

const foodChainRegistrationRequestSchema = new mongoose.Schema({
  // Contact Person Details
  contactPersonName: { type: String, required: true },
  email: { type: String, required: true },
  phone: { type: String, required: true },
  
  // Business Details
  businessName: { type: String, required: true },
  businessType: {
    type: String,
    enum: [
      "individual",
      "proprietorship", 
      "partnership",
      "llp",
      "private_limited",
      "public_limited",
      "trust",
      "society",
      "ngo",
      "educational_institutes",
      "not_yet_registered",
      "other",
    ],
    required: true,
  },
  subcategory: {
    type: String,
    enum: [
      "restaurant",
      "online_food_ordering",
      "food_court",
      "catering",
      "alcohol",
      "restaurant_search_and_booking",
      "dairy_products",
      "bakeries",
    ],
    default: "restaurant",
  },
  
  // Location Details
  city: { type: String, required: true },
  state: { type: String, required: true },
  address: { type: String },
  
  // Additional Information
  message: { type: String },
  estimatedOutlets: { type: Number, default: 1 },
  website: { type: String },
  
  // Request Status
  status: {
    type: String,
    enum: ["pending", "approved", "rejected", "contacted"],
    default: "pending",
  },
  
  // Admin Notes
  adminNotes: { type: String },
  reviewedBy: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
  reviewedAt: { type: Date },
  
  // Created Food Chain (if approved)
  createdFoodChainId: { type: mongoose.Schema.Types.ObjectId, ref: "FoodChain" },
  
  // Timestamps
  createdAt: { type: Date, default: Date.now },
  updatedAt: { type: Date, default: Date.now },
});

// Update the updatedAt field before saving
foodChainRegistrationRequestSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

export default mongoose.model("FoodChainRegistrationRequest", foodChainRegistrationRequestSchema);
